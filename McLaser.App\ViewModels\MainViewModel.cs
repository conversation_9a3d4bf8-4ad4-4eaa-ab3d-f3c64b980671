using System;
using System.Collections.ObjectModel;
using System.Windows.Input;
using McLaser.Core.Common;
using McLaser.Core.Framework.Configuration;
using McLaser.Core.Framework.Container;
using McLaser.Core.Framework.Logging;
using McLaser.Core.Framework.Services;
using McLaser.Core.Framework.UI;
using McLaser.App.Views;

namespace McLaser.App.ViewModels
{
    /// <summary>
    /// 主窗口ViewModel
    /// 展示McLaser.Core框架的MVVM模式使用
    /// </summary>
    public class MainViewModel : ViewModelBase
    {
        private readonly IContainer _container;
        private readonly IThemeService _themeService;
        private readonly IWindowManager _windowManager;
        private readonly IDialogService _dialogService;
        private readonly IConfigurationService _configurationService;
        private readonly ILogger _logger;

        private string _title = "McLaser示例应用程序";
        private string _statusMessage = "就绪";
        private string _currentTheme = "Light";
        private bool _isThemeMenuOpen;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="container">DI容器</param>
        /// <param name="themeService">主题服务</param>
        /// <param name="windowManager">窗口管理器</param>
        /// <param name="dialogService">对话框服务</param>
        /// <param name="configurationService">配置服务</param>
        /// <param name="logger">日志服务</param>
        public MainViewModel(
            IContainer container,
            IThemeService themeService,
            IWindowManager windowManager,
            IDialogService dialogService,
            IConfigurationService configurationService,
            ILogger logger)
        {
            _container = container ?? throw new ArgumentNullException(nameof(container));
            _themeService = themeService ?? throw new ArgumentNullException(nameof(themeService));
            _windowManager = windowManager ?? throw new ArgumentNullException(nameof(windowManager));
            _dialogService = dialogService ?? throw new ArgumentNullException(nameof(dialogService));
            _configurationService = configurationService ?? throw new ArgumentNullException(nameof(configurationService));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));

            InitializeCommands();
            InitializeData();
            SubscribeToEvents();
        }

        #region 属性

        /// <summary>
        /// 窗口标题
        /// </summary>
        public string Title
        {
            get => _title;
            set => SetProperty(ref _title, value);
        }

        /// <summary>
        /// 状态消息
        /// </summary>
        public string StatusMessage
        {
            get => _statusMessage;
            set => SetProperty(ref _statusMessage, value);
        }

        /// <summary>
        /// 当前主题
        /// </summary>
        public string CurrentTheme
        {
            get => _currentTheme;
            set => SetProperty(ref _currentTheme, value);
        }

        /// <summary>
        /// 主题菜单是否打开
        /// </summary>
        public bool IsThemeMenuOpen
        {
            get => _isThemeMenuOpen;
            set => SetProperty(ref _isThemeMenuOpen, value);
        }

        /// <summary>
        /// 可用主题列表
        /// </summary>
        public ObservableCollection<string> AvailableThemes { get; } = new ObservableCollection<string>();

        /// <summary>
        /// 活动窗口列表
        /// </summary>
        public ObservableCollection<string> ActiveWindows { get; } = new ObservableCollection<string>();

        #endregion

        #region 命令

        /// <summary>
        /// 切换主题命令
        /// </summary>
        public ICommand SwitchThemeCommand { get; private set; } = null!;

        /// <summary>
        /// 打开设置窗口命令
        /// </summary>
        public ICommand OpenSettingsCommand { get; private set; } = null!;

        /// <summary>
        /// 打开数据输入窗口命令
        /// </summary>
        public ICommand OpenDataInputCommand { get; private set; } = null!;

        /// <summary>
        /// 显示关于对话框命令
        /// </summary>
        public ICommand ShowAboutCommand { get; private set; } = null!;

        /// <summary>
        /// 退出应用程序命令
        /// </summary>
        public ICommand ExitCommand { get; private set; } = null!;

        /// <summary>
        /// 刷新状态命令
        /// </summary>
        public ICommand RefreshStatusCommand { get; private set; } = null!;

        /// <summary>
        /// 测试对话框命令
        /// </summary>
        public ICommand TestDialogCommand { get; private set; } = null!;

        /// <summary>
        /// 测试配置命令
        /// </summary>
        public ICommand TestConfigurationCommand { get; private set; } = null!;

        #endregion

        #region 私有方法

        /// <summary>
        /// 初始化命令
        /// </summary>
        private void InitializeCommands()
        {
            SwitchThemeCommand = new RelayCommand<string>(ExecuteSwitchTheme, CanExecuteSwitchTheme);
            OpenSettingsCommand = new RelayCommand(ExecuteOpenSettings);
            OpenDataInputCommand = new RelayCommand(ExecuteOpenDataInput);
            ShowAboutCommand = new RelayCommand(ExecuteShowAbout);
            ExitCommand = new RelayCommand(ExecuteExit);
            RefreshStatusCommand = new RelayCommand(ExecuteRefreshStatus);
            TestDialogCommand = new RelayCommand(ExecuteTestDialog);
            TestConfigurationCommand = new RelayCommand(ExecuteTestConfiguration);
        }

        /// <summary>
        /// 初始化数据
        /// </summary>
        private void InitializeData()
        {
            try
            {
                // 加载可用主题
                LoadAvailableThemes();

                // 设置当前主题
                CurrentTheme = _themeService.CurrentTheme;

                // 更新状态
                UpdateStatus("应用程序已就绪");

                _logger.LogInfo("MainViewModel初始化完成");
            }
            catch (Exception ex)
            {
                _logger.LogError($"MainViewModel初始化失败: {ex.Message}", ex);
                UpdateStatus($"初始化失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 订阅事件
        /// </summary>
        private void SubscribeToEvents()
        {
            // 订阅主题变更事件
            _themeService.ThemeChanged += OnThemeChanged;

            // 订阅窗口事件
            _windowManager.WindowOpened += OnWindowOpened;
            _windowManager.WindowClosed += OnWindowClosed;
        }

        /// <summary>
        /// 加载可用主题
        /// </summary>
        private void LoadAvailableThemes()
        {
            AvailableThemes.Clear();
            foreach (var theme in _themeService.AvailableThemes)
            {
                AvailableThemes.Add(theme);
            }
        }

        /// <summary>
        /// 更新状态消息
        /// </summary>
        /// <param name="message">状态消息</param>
        private void UpdateStatus(string message)
        {
            StatusMessage = $"{DateTime.Now:HH:mm:ss} - {message}";
        }

        /// <summary>
        /// 更新活动窗口列表
        /// </summary>
        private void UpdateActiveWindows()
        {
            ActiveWindows.Clear();
            foreach (var window in _windowManager.ActiveWindows)
            {
                ActiveWindows.Add($"{window.GetType().Name} - {window.Title}");
            }
        }

        #endregion

        #region 命令执行方法

        /// <summary>
        /// 执行切换主题命令
        /// </summary>
        /// <param name="themeName">主题名称</param>
        private void ExecuteSwitchTheme(string? themeName)
        {
            if (string.IsNullOrEmpty(themeName)) return;

            try
            {
                if (_themeService.ApplyTheme(themeName))
                {
                    CurrentTheme = themeName;
                    UpdateStatus($"已切换到 {themeName} 主题");
                    _logger.LogInfo($"主题已切换到: {themeName}");
                }
                else
                {
                    UpdateStatus($"切换主题失败: {themeName}");
                    _logger.LogWarning($"主题切换失败: {themeName}");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError($"切换主题时发生错误: {ex.Message}", ex);
                UpdateStatus($"切换主题错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 判断是否可以执行切换主题命令
        /// </summary>
        /// <param name="themeName">主题名称</param>
        /// <returns>是否可以执行</returns>
        private bool CanExecuteSwitchTheme(string? themeName)
        {
            return !string.IsNullOrEmpty(themeName) && themeName != CurrentTheme;
        }

        /// <summary>
        /// 执行打开设置窗口命令
        /// </summary>
        private void ExecuteOpenSettings()
        {
            try
            {
                var settingsViewModel = _container.Resolve<SettingsViewModel>();
                var settingsWindow = new SettingsWindow
                {
                    DataContext = settingsViewModel,
                    Owner = _windowManager.MainWindow
                };

                _windowManager.ShowWindow(settingsWindow, _windowManager.MainWindow);
                UpdateStatus("设置窗口已打开");
            }
            catch (Exception ex)
            {
                _logger.LogError($"打开设置窗口失败: {ex.Message}", ex);
                _dialogService.ShowError($"打开设置窗口失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 执行打开数据输入窗口命令
        /// </summary>
        private void ExecuteOpenDataInput()
        {
            try
            {
                var dataInputViewModel = _container.Resolve<DataInputViewModel>();
                var dataInputWindow = new DataInputWindow
                {
                    DataContext = dataInputViewModel,
                    Owner = _windowManager.MainWindow
                };

                _windowManager.ShowWindow(dataInputWindow, _windowManager.MainWindow);
                UpdateStatus("数据输入窗口已打开");
            }
            catch (Exception ex)
            {
                _logger.LogError($"打开数据输入窗口失败: {ex.Message}", ex);
                _dialogService.ShowError($"打开数据输入窗口失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 执行显示关于对话框命令
        /// </summary>
        private void ExecuteShowAbout()
        {
            var aboutMessage = "McLaser示例应用程序\n\n" +
                              "版本: 1.0.0\n" +
                              "基于: McLaser.Core框架\n\n" +
                              "这是一个展示McLaser.Core框架功能的完整示例应用程序。\n\n" +
                              "功能特性:\n" +
                              "• 统一DI容器架构\n" +
                              "• 主题管理系统\n" +
                              "• 窗口管理器\n" +
                              "• 数据验证框架\n" +
                              "• 配置管理服务\n" +
                              "• MVVM模式支持";

            _dialogService.ShowInformation(aboutMessage, "关于");
            UpdateStatus("已显示关于信息");
        }

        /// <summary>
        /// 执行退出应用程序命令
        /// </summary>
        private void ExecuteExit()
        {
            var result = _dialogService.ShowConfirmation("确定要退出应用程序吗？", "确认退出");
            if (result)
            {
                System.Windows.Application.Current.Shutdown();
            }
        }

        /// <summary>
        /// 执行刷新状态命令
        /// </summary>
        private void ExecuteRefreshStatus()
        {
            try
            {
                // 刷新主题信息
                CurrentTheme = _themeService.CurrentTheme;
                LoadAvailableThemes();

                // 刷新窗口信息
                UpdateActiveWindows();

                UpdateStatus("状态已刷新");
            }
            catch (Exception ex)
            {
                _logger.LogError($"刷新状态失败: {ex.Message}", ex);
                UpdateStatus($"刷新失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 执行测试对话框命令
        /// </summary>
        private void ExecuteTestDialog()
        {
            try
            {
                // 测试不同类型的对话框
                _dialogService.ShowInformation("这是一个信息对话框示例。", "信息");

                var warning = _dialogService.ShowConfirmation("这是一个确认对话框示例。\n是否继续？", "确认");
                if (warning)
                {
                    _dialogService.ShowInformation("您选择了继续。", "结果");
                }

                UpdateStatus("对话框测试完成");
            }
            catch (Exception ex)
            {
                _logger.LogError($"测试对话框失败: {ex.Message}", ex);
                UpdateStatus($"对话框测试失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 执行测试配置命令
        /// </summary>
        private void ExecuteTestConfiguration()
        {
            try
            {
                // 测试配置服务
                var testKey = "TestConfiguration";
                var testValue = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");

                _configurationService.SetValue(testKey, testValue);
                var retrievedValue = _configurationService.GetValue<string>(testKey);

                var message = $"配置测试成功！\n\n设置值: {testValue}\n获取值: {retrievedValue}";
                _dialogService.ShowInformation(message, "配置测试");

                UpdateStatus("配置测试完成");
            }
            catch (Exception ex)
            {
                _logger.LogError($"测试配置失败: {ex.Message}", ex);
                _dialogService.ShowError($"配置测试失败：{ex.Message}");
            }
        }

        #endregion

        #region 事件处理

        /// <summary>
        /// 主题变更事件处理
        /// </summary>
        private void OnThemeChanged(object? sender, ThemeChangedEventArgs e)
        {
            CurrentTheme = e.NewTheme;
            UpdateStatus($"主题已变更: {e.OldTheme} -> {e.NewTheme}");
        }

        /// <summary>
        /// 窗口打开事件处理
        /// </summary>
        private void OnWindowOpened(object? sender, WindowEventArgs e)
        {
            UpdateActiveWindows();
            UpdateStatus($"窗口已打开: {e.Window.GetType().Name}");
        }

        /// <summary>
        /// 窗口关闭事件处理
        /// </summary>
        private void OnWindowClosed(object? sender, WindowEventArgs e)
        {
            UpdateActiveWindows();
            UpdateStatus($"窗口已关闭: {e.Window.GetType().Name}");
        }

        #endregion

        #region 清理

        /// <summary>
        /// 清理资源
        /// </summary>
        protected override void OnDisposing()
        {
            // 取消事件订阅
            _themeService.ThemeChanged -= OnThemeChanged;
            _windowManager.WindowOpened -= OnWindowOpened;
            _windowManager.WindowClosed -= OnWindowClosed;

            base.OnDisposing();
        }

        #endregion
    }
}
