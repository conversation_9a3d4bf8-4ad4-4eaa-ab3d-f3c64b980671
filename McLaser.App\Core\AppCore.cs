using System;
using System.Windows;
using McLaser.Core.Framework.Bootstrapper;
using McLaser.Core.Framework.Configuration;
using McLaser.Core.Framework.Container;
using McLaser.Core.Framework.Logging;
using McLaser.Core.Framework.Services;
using McLaser.Core.Framework.UI;
using McLaser.App.ViewModels;
using McLaser.Core.Framework;

namespace McLaser.App.Core
{
    /// <summary>
    /// McLaser示例应用程序核心类
    /// 继承自McLaser.Core的ApplicationCoreBase，展示框架的完整使用
    /// </summary>
    public class AppCore : ApplicationCoreBase
    {
        private IThemeService? _themeService;
        private IWindowManager? _windowManager;
        private ILogger? _logger;

        /// <summary>
        /// 构造函数
        /// </summary>
        public AppCore() : base()
        {
            // 设置应用程序信息
            ApplicationInfo.Name = "McLaser示例应用程序";
            ApplicationInfo.Version = "1.0.0";
            ApplicationInfo.Description = "展示McLaser.Core框架功能的完整示例应用程序";
            ApplicationInfo.Company = "McLaser Framework";
        }

        /// <summary>
        /// 配置服务注册
        /// 注册应用程序特定的服务和组件
        /// </summary>
        protected override void ConfigureServices()
        {
            // 调用基类方法注册核心服务
            base.ConfigureServices();

            try
            {
                // 注册UI增强服务
                Container.RegisterSingleton<IThemeService, ThemeManager>();
                Container.RegisterSingleton<IWindowManager, WindowManager>();

                // 注册ViewModel
                Container.RegisterTransient<MainViewModel>();
                Container.RegisterTransient<SettingsViewModel>();
                Container.RegisterTransient<DataInputViewModel>();

                // 注册应用程序特定服务
                RegisterApplicationServices();

                Logger?.LogInfo("服务注册完成");
            }
            catch (Exception ex)
            {
                Logger?.LogError($"服务注册失败: {ex.Message}", ex);
                throw;
            }
        }

        /// <summary>
        /// 应用程序初始化
        /// 在服务注册完成后进行应用程序特定的初始化
        /// </summary>
        protected override void OnInitialized()
        {
            base.OnInitialized();

            try
            {
                // 获取核心服务
                _themeService = Container.Resolve<IThemeService>();
                _windowManager = Container.Resolve<IWindowManager>();
                _logger = Container.Resolve<ILogger>();

                // 初始化主题系统
                InitializeThemeSystem();

                // 初始化窗口管理
                InitializeWindowManager();

                // 加载应用程序配置
                LoadApplicationSettings();

                _logger?.LogInfo("应用程序初始化完成");
            }
            catch (Exception ex)
            {
                Logger?.LogError($"应用程序初始化失败: {ex.Message}", ex);
                throw;
            }
        }

        /// <summary>
        /// 应用程序启动
        /// </summary>
        public void Run()
        {
            try
            {
                // 确保应用程序已初始化
                if (!IsInitialized)
                {
                    Initialize();
                }

                // 创建并显示主窗口
                var mainWindow = CreateMainWindow();
                if (mainWindow != null)
                {
                    _windowManager?.ShowWindow(mainWindow);
                    _logger?.LogInfo("主窗口已显示");
                }
                else
                {
                    throw new InvalidOperationException("无法创建主窗口");
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError($"应用程序启动失败: {ex.Message}", ex);
                
                // 显示错误对话框
                var dialogService = Container.Resolve<IDialogService>();
                dialogService?.ShowError($"应用程序启动失败：{ex.Message}");
                
                throw;
            }
        }

        /// <summary>
        /// 注册应用程序特定服务
        /// </summary>
        private void RegisterApplicationServices()
        {
            // 这里可以注册应用程序特定的服务
            // 例如：业务服务、数据服务等
            
            _logger?.LogInfo("应用程序特定服务注册完成");
        }

        /// <summary>
        /// 初始化主题系统
        /// </summary>
        private void InitializeThemeSystem()
        {
            if (_themeService == null) return;

            try
            {
                // 注册应用程序主题
                RegisterApplicationThemes();

                // 加载保存的主题设置
                _themeService.LoadSavedTheme();

                // 如果没有保存的主题，使用默认主题
                if (string.IsNullOrEmpty(_themeService.CurrentTheme) || 
                    _themeService.CurrentTheme == "Default")
                {
                    var defaultTheme = ConfigurationService?.GetValue<string>("DefaultTheme") ?? "Light";
                    _themeService.ApplyTheme(defaultTheme);
                }

                _logger?.LogInfo($"主题系统初始化完成，当前主题: {_themeService.CurrentTheme}");
            }
            catch (Exception ex)
            {
                _logger?.LogError($"主题系统初始化失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 注册应用程序主题
        /// </summary>
        private void RegisterApplicationThemes()
        {
            if (_themeService == null) return;

            try
            {
                // 注册浅色主题
                var lightThemeUri = new Uri("pack://application:,,,/McLaser.App;component/Themes/LightTheme.xaml");
                _themeService.RegisterTheme("Light", lightThemeUri);

                // 注册深色主题
                var darkThemeUri = new Uri("pack://application:,,,/McLaser.App;component/Themes/DarkTheme.xaml");
                _themeService.RegisterTheme("Dark", darkThemeUri);

                _logger?.LogInfo("应用程序主题注册完成");
            }
            catch (Exception ex)
            {
                _logger?.LogError($"主题注册失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 初始化窗口管理器
        /// </summary>
        private void InitializeWindowManager()
        {
            if (_windowManager == null) return;

            try
            {
                // 订阅窗口事件
                _windowManager.WindowOpened += OnWindowOpened;
                _windowManager.WindowClosed += OnWindowClosed;
                _windowManager.WindowActivated += OnWindowActivated;

                _logger?.LogInfo("窗口管理器初始化完成");
            }
            catch (Exception ex)
            {
                _logger?.LogError($"窗口管理器初始化失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 加载应用程序设置
        /// </summary>
        private void LoadApplicationSettings()
        {
            try
            {
                // 从配置服务加载设置
                var autoSave = ConfigurationService?.GetValue<bool>("AutoSaveSettings") ?? true;
                var rememberWindowState = ConfigurationService?.GetValue<bool>("WindowStateRemember") ?? true;
                var cacheEnabled = ConfigurationService?.GetValue<bool>("CacheEnabled") ?? true;
                var performanceMonitoring = ConfigurationService?.GetValue<bool>("PerformanceMonitoring") ?? true;

                _logger?.LogInfo($"应用程序设置加载完成 - 自动保存: {autoSave}, 记住窗口状态: {rememberWindowState}");
            }
            catch (Exception ex)
            {
                _logger?.LogError($"加载应用程序设置失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 创建主窗口
        /// </summary>
        private Window? CreateMainWindow()
        {
            try
            {
                var mainViewModel = Container.Resolve<MainViewModel>();
                var mainWindow = new Views.MainWindow
                {
                    DataContext = mainViewModel
                };

                // 设置为主窗口
                _windowManager?.MainWindow = mainWindow;
                Application.Current.MainWindow = mainWindow;

                return mainWindow;
            }
            catch (Exception ex)
            {
                _logger?.LogError($"创建主窗口失败: {ex.Message}", ex);
                return null;
            }
        }

        /// <summary>
        /// 窗口打开事件处理
        /// </summary>
        private void OnWindowOpened(object? sender, WindowEventArgs e)
        {
            _logger?.LogInfo($"窗口已打开: {e.Window.GetType().Name} (ID: {e.WindowId})");
        }

        /// <summary>
        /// 窗口关闭事件处理
        /// </summary>
        private void OnWindowClosed(object? sender, WindowEventArgs e)
        {
            _logger?.LogInfo($"窗口已关闭: {e.Window.GetType().Name} (ID: {e.WindowId})");
        }

        /// <summary>
        /// 窗口激活事件处理
        /// </summary>
        private void OnWindowActivated(object? sender, WindowEventArgs e)
        {
            _logger?.LogInfo($"窗口已激活: {e.Window.GetType().Name} (ID: {e.WindowId})");
        }

        /// <summary>
        /// 应用程序关闭清理
        /// </summary>
        protected override void OnDisposing()
        {
            try
            {
                // 保存应用程序设置
                SaveApplicationSettings();

                // 保存主题设置
                _themeService?.SaveCurrentTheme();

                // 保存窗口状态
                _windowManager?.SaveAllWindowStates();

                // 取消事件订阅
                if (_windowManager != null)
                {
                    _windowManager.WindowOpened -= OnWindowOpened;
                    _windowManager.WindowClosed -= OnWindowClosed;
                    _windowManager.WindowActivated -= OnWindowActivated;
                }

                _logger?.LogInfo("应用程序清理完成");
            }
            catch (Exception ex)
            {
                _logger?.LogError($"应用程序清理失败: {ex.Message}", ex);
            }

            base.OnDisposing();
        }

        /// <summary>
        /// 保存应用程序设置
        /// </summary>
        private void SaveApplicationSettings()
        {
            try
            {
                // 保存当前配置到配置服务
                ConfigurationService?.Save();
                _logger?.LogInfo("应用程序设置已保存");
            }
            catch (Exception ex)
            {
                _logger?.LogError($"保存应用程序设置失败: {ex.Message}", ex);
            }
        }
    }
}
