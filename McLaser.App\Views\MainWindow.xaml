<Window x:Class="McLaser.App.Views.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="{Binding Title}"
        Height="600" Width="900"
        MinHeight="500" MinWidth="700"
        WindowStartupLocation="CenterScreen">
    
    <Window.Resources>
        <!-- 窗口特定资源 -->
        <Style x:Key="MenuItemStyle" TargetType="MenuItem">
            <Setter Property="Padding" value="8,4" />
            <Setter Property="Margin" value="2" />
        </Style>
        
        <Style x:Key="ToolBarButtonStyle" TargetType="Button">
            <Setter Property="Padding" value="6,4" />
            <Setter Property="Margin" value="2" />
            <Setter Property="MinWidth" value="60" />
        </Style>
        
        <Style x:Key="StatusBarTextStyle" TargetType="TextBlock">
            <Setter Property="Margin" value="4,2" />
            <Setter Property="VerticalAlignment" value="Center" />
        </Style>
    </Window.Resources>
    
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="Auto" />
            <RowDefinition Height="*" />
            <RowDefinition Height="Auto" />
        </Grid.RowDefinitions>
        
        <!-- 菜单栏 -->
        <Menu Grid.Row="0" Background="{DynamicResource MenuBackgroundBrush}">
            <MenuItem Header="文件(_F)" Style="{StaticResource MenuItemStyle}">
                <MenuItem Header="新建(_N)" InputGestureText="Ctrl+N" />
                <MenuItem Header="打开(_O)" InputGestureText="Ctrl+O" />
                <MenuItem Header="保存(_S)" InputGestureText="Ctrl+S" />
                <Separator />
                <MenuItem Header="退出(_X)" Command="{Binding ExitCommand}" InputGestureText="Alt+F4" />
            </MenuItem>
            
            <MenuItem Header="编辑(_E)" Style="{StaticResource MenuItemStyle}">
                <MenuItem Header="撤销(_U)" InputGestureText="Ctrl+Z" />
                <MenuItem Header="重做(_R)" InputGestureText="Ctrl+Y" />
                <Separator />
                <MenuItem Header="复制(_C)" InputGestureText="Ctrl+C" />
                <MenuItem Header="粘贴(_V)" InputGestureText="Ctrl+V" />
            </MenuItem>
            
            <MenuItem Header="视图(_V)" Style="{StaticResource MenuItemStyle}">
                <MenuItem Header="主题(_T)">
                    <MenuItem Header="浅色主题" Command="{Binding SwitchThemeCommand}" CommandParameter="Light" />
                    <MenuItem Header="深色主题" Command="{Binding SwitchThemeCommand}" CommandParameter="Dark" />
                </MenuItem>
                <Separator />
                <MenuItem Header="刷新状态(_R)" Command="{Binding RefreshStatusCommand}" />
            </MenuItem>
            
            <MenuItem Header="工具(_T)" Style="{StaticResource MenuItemStyle}">
                <MenuItem Header="设置(_S)" Command="{Binding OpenSettingsCommand}" />
                <MenuItem Header="数据输入(_D)" Command="{Binding OpenDataInputCommand}" />
                <Separator />
                <MenuItem Header="测试对话框(_T)" Command="{Binding TestDialogCommand}" />
                <MenuItem Header="测试配置(_C)" Command="{Binding TestConfigurationCommand}" />
            </MenuItem>
            
            <MenuItem Header="帮助(_H)" Style="{StaticResource MenuItemStyle}">
                <MenuItem Header="关于(_A)" Command="{Binding ShowAboutCommand}" />
            </MenuItem>
        </Menu>
        
        <!-- 工具栏 -->
        <ToolBar Grid.Row="1" Background="{DynamicResource ToolBarBackgroundBrush}">
            <Button Content="设置" Command="{Binding OpenSettingsCommand}" Style="{StaticResource ToolBarButtonStyle}" 
                    ToolTip="打开设置窗口" />
            <Button Content="数据输入" Command="{Binding OpenDataInputCommand}" Style="{StaticResource ToolBarButtonStyle}" 
                    ToolTip="打开数据输入窗口" />
            <Separator />
            <Button Content="浅色" Command="{Binding SwitchThemeCommand}" CommandParameter="Light" 
                    Style="{StaticResource ToolBarButtonStyle}" ToolTip="切换到浅色主题" />
            <Button Content="深色" Command="{Binding SwitchThemeCommand}" CommandParameter="Dark" 
                    Style="{StaticResource ToolBarButtonStyle}" ToolTip="切换到深色主题" />
            <Separator />
            <Button Content="刷新" Command="{Binding RefreshStatusCommand}" Style="{StaticResource ToolBarButtonStyle}" 
                    ToolTip="刷新状态信息" />
        </ToolBar>
        
        <!-- 主内容区域 -->
        <Grid Grid.Row="2" Margin="10">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="2*" />
                <ColumnDefinition Width="5" />
                <ColumnDefinition Width="*" />
            </Grid.ColumnDefinitions>
            
            <!-- 左侧面板 -->
            <GroupBox Grid.Column="0" Header="McLaser.Core框架功能演示" Padding="10">
                <ScrollViewer VerticalScrollBarVisibility="Auto">
                    <StackPanel>
                        <!-- 框架信息 -->
                        <TextBlock Text="McLaser.Core框架特性：" FontWeight="Bold" Margin="0,0,0,10" />
                        
                        <TextBlock TextWrapping="Wrap" Margin="0,0,0,15">
                            <Run Text="• 统一DI容器架构" /><LineBreak />
                            <Run Text="• 主题管理系统" /><LineBreak />
                            <Run Text="• 窗口管理器" /><LineBreak />
                            <Run Text="• 数据验证框架" /><LineBreak />
                            <Run Text="• 配置管理服务" /><LineBreak />
                            <Run Text="• MVVM模式支持" /><LineBreak />
                            <Run Text="• 日志记录系统" /><LineBreak />
                            <Run Text="• 对话框服务" /><LineBreak />
                            <Run Text="• 导航服务" /><LineBreak />
                            <Run Text="• 异常处理服务" />
                        </TextBlock>
                        
                        <!-- 功能测试按钮 -->
                        <TextBlock Text="功能测试：" FontWeight="Bold" Margin="0,0,0,10" />
                        
                        <StackPanel Orientation="Horizontal" Margin="0,0,0,10">
                            <Button Content="打开设置" Command="{Binding OpenSettingsCommand}" 
                                    Margin="0,0,10,0" Padding="10,5" />
                            <Button Content="数据输入" Command="{Binding OpenDataInputCommand}" 
                                    Margin="0,0,10,0" Padding="10,5" />
                        </StackPanel>
                        
                        <StackPanel Orientation="Horizontal" Margin="0,0,0,10">
                            <Button Content="测试对话框" Command="{Binding TestDialogCommand}" 
                                    Margin="0,0,10,0" Padding="10,5" />
                            <Button Content="测试配置" Command="{Binding TestConfigurationCommand}" 
                                    Margin="0,0,10,0" Padding="10,5" />
                        </StackPanel>
                        
                        <!-- 主题切换 -->
                        <TextBlock Text="主题切换：" FontWeight="Bold" Margin="0,15,0,10" />
                        
                        <StackPanel Orientation="Horizontal" Margin="0,0,0,10">
                            <TextBlock Text="当前主题：" VerticalAlignment="Center" Margin="0,0,10,0" />
                            <TextBlock Text="{Binding CurrentTheme}" FontWeight="Bold" VerticalAlignment="Center" 
                                       Foreground="{DynamicResource AccentBrush}" />
                        </StackPanel>
                        
                        <StackPanel Orientation="Horizontal">
                            <Button Content="浅色主题" Command="{Binding SwitchThemeCommand}" CommandParameter="Light" 
                                    Margin="0,0,10,0" Padding="10,5" />
                            <Button Content="深色主题" Command="{Binding SwitchThemeCommand}" CommandParameter="Dark" 
                                    Padding="10,5" />
                        </StackPanel>
                        
                        <!-- 可用主题列表 -->
                        <TextBlock Text="可用主题：" FontWeight="Bold" Margin="0,15,0,10" />
                        <ItemsControl ItemsSource="{Binding AvailableThemes}">
                            <ItemsControl.ItemTemplate>
                                <DataTemplate>
                                    <Button Content="{Binding}" Command="{Binding DataContext.SwitchThemeCommand, 
                                            RelativeSource={RelativeSource AncestorType=Window}}" 
                                            CommandParameter="{Binding}" 
                                            Margin="2" Padding="8,4" HorizontalAlignment="Left" />
                                </DataTemplate>
                            </ItemsControl.ItemTemplate>
                            <ItemsControl.ItemsPanel>
                                <ItemsPanelTemplate>
                                    <WrapPanel />
                                </ItemsPanelTemplate>
                            </ItemsControl.ItemsPanel>
                        </ItemsControl>
                    </StackPanel>
                </ScrollViewer>
            </GroupBox>
            
            <!-- 分隔符 -->
            <GridSplitter Grid.Column="1" HorizontalAlignment="Stretch" Background="{DynamicResource BorderBrush}" />
            
            <!-- 右侧面板 -->
            <GroupBox Grid.Column="2" Header="系统信息" Padding="10">
                <ScrollViewer VerticalScrollBarVisibility="Auto">
                    <StackPanel>
                        <!-- 活动窗口 -->
                        <TextBlock Text="活动窗口：" FontWeight="Bold" Margin="0,0,0,10" />
                        <ListBox ItemsSource="{Binding ActiveWindows}" Height="120" Margin="0,0,0,15" />
                        
                        <!-- 系统状态 -->
                        <TextBlock Text="系统状态：" FontWeight="Bold" Margin="0,0,0,10" />
                        <StackPanel Margin="0,0,0,15">
                            <TextBlock Text="{Binding StatusMessage}" TextWrapping="Wrap" />
                        </StackPanel>
                        
                        <!-- 操作按钮 -->
                        <Button Content="刷新状态" Command="{Binding RefreshStatusCommand}" 
                                Margin="0,0,0,10" Padding="8,5" />
                        <Button Content="关于" Command="{Binding ShowAboutCommand}" 
                                Margin="0,0,0,10" Padding="8,5" />
                        <Button Content="退出" Command="{Binding ExitCommand}" 
                                Padding="8,5" />
                    </StackPanel>
                </ScrollViewer>
            </GroupBox>
        </Grid>
        
        <!-- 状态栏 -->
        <StatusBar Grid.Row="3" Background="{DynamicResource StatusBarBackgroundBrush}">
            <StatusBarItem>
                <TextBlock Text="{Binding StatusMessage}" Style="{StaticResource StatusBarTextStyle}" />
            </StatusBarItem>
            <Separator />
            <StatusBarItem>
                <TextBlock Text="{Binding CurrentTheme, StringFormat='主题: {0}'}" 
                           Style="{StaticResource StatusBarTextStyle}" />
            </StatusBarItem>
            <Separator />
            <StatusBarItem>
                <TextBlock Text="{Binding ActiveWindows.Count, StringFormat='窗口: {0}'}" 
                           Style="{StaticResource StatusBarTextStyle}" />
            </StatusBarItem>
        </StatusBar>
    </Grid>
</Window>
